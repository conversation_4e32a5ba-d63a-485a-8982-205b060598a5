import 'dart:convert';
import 'dart:io';

import 'package:arabic_sign_language/bloc/VideoTranscription/video_transcription_bloc.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';
import 'package:chewie/chewie.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

import 'package:flutter/material.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../core/unity_controller.dart';
import 'package:flutter/widgets.dart' as ui;

class VideoTranscriptScreen extends StatefulWidget {
  const VideoTranscriptScreen({super.key});

  @override
  State<VideoTranscriptScreen> createState() => _VideoTranscriptScreenState();
}

class _VideoTranscriptScreenState extends State<VideoTranscriptScreen>
    with WidgetsBindingObserver {
  final TextEditingController _videoLinkController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  UnityWidgetController? _unityWidgetController;
  YoutubePlayerController? _youtubePlayerController;
  ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  List<String> messagesFromUnity = [];
  List<String> currentMessageList = [];
  List<String> currentProcessingTexts = [];
  late VideoTranscriptionBloc _videoTranscriptionBloc;
  final ValueNotifier<double> progressNotifier = ValueNotifier(0.0);

  List<List<String>> allWords = [];
  final ScrollController _scrollController = ScrollController();
  int activeSectionIndex = 0;
  List<String> lastWords = [];

  // Slider value for animation speed control (0.0 to 1.5)
  double sliderValue = 1.0;

  int _selectedTabIndex = 0;
  final List<String> _tabTitles = [
    'youtube_video',
    'upload_video',
  ];

  PlatformFile? _videoFile;
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;

  // Communication from  Flutter to Unity
  Future<void> sendMessageToUnity(Map<String, dynamic> message) async {
    try {
      message['screen'] = "VideoTranscriptScreen";
      String jsonString = json.encode(message);
      print("jsonString =>$jsonString");
      await _unityWidgetController?.postMessage(
          'SaudiCharacter', 'PlaySignAnim', jsonString);
    } catch (e) {
      print("Error sending message to Unity: $e");
    }
  }

  Future<void> stopCurrentAnimation() async {
    if (!mounted) return; // Add mounted check

    context
        .read<VideoTranscriptionBloc>()
        .add(const UpdateCurrentAnimationText(animationText: ""));
    if (_unityWidgetController != null) {
      await _unityWidgetController?.postMessage(
          'SaudiCharacter', 'StopAnimations', "");
    }
  }

  Future<void> adjustAnimationSpeed(String value) async {
    try {
      if (_unityWidgetController != null) {
        await _unityWidgetController?.postMessage(
            "SaudiCharacter", 'SetAnimationSpeed', value);
      }
    } catch (e) {
      print("Error adjusting animation speed: $e");
    }
  }

  void pushToSecond(String value) {
    if (!mounted) return; // Add mounted check

    currentProcessingTexts.add(value); // Add the value to `second`.

    // Remove spaces for processing clarity
    String combined = currentProcessingTexts.join().replaceAll(' ', '');

    if (!mounted) return; // Add another mounted check before accessing context

    String currentFirstElement = context
        .read<VideoTranscriptionBloc>()
        .rootWords[currentIndex.value]
        .replaceAll(',', '')
        .replaceAll(' ', '');

    print("Updated second: $combined , ${currentFirstElement}");

    if (combined == currentFirstElement) {
      // If the combined value matches the current `first` element.
      currentIndex.value++; // Move to the next element in `first`.
      // Clear `second` for the next processing if necessary.
      currentProcessingTexts.clear();
    } else if (combined.length > currentFirstElement.length) {
      // If combined exceeds the current `first` element, log a warning.
      print("Warning: Combined value exceeds the current `first` element.");
    } else {
      print("Currently processing index ${currentIndex.value}");
    }
  }

  // void processMatching(List<String> first, List<String> second) {
  //   int firstIndex = 0;
  //   String buffer = "";

  //   for (String element in second) {
  //     buffer += element; // Add the element to the buffer.
  //     print("Buffer: $buffer"); // Debugging line to see progress.

  //     // Check if the current buffer matches the current element in `first`.
  //     if (buffer == first[firstIndex].replaceAll(',', '')) {
  //       print("Matched: ${first[firstIndex]} at index $firstIndex");
  //       firstIndex++; // Move to the next element in `first`.
  //       buffer = ""; // Clear the buffer after a match.

  //       // Stop if all elements in `first` are processed.
  //       if (firstIndex >= first.length) {
  //         print("Processing complete!");
  //         break;
  //       }
  //     }
  //   }

  //   // Check if any unmatched elements remain in `second`.
  //   if (firstIndex < first.length) {
  //     print("Unprocessed part of first: ${first.sublist(firstIndex)}");
  //   }
  // }

  // Communication from Unity to Flutter
  void onUnityMessage(message) {
    if (!mounted)
      return; // Add this check to prevent accessing context after disposal

    if (message.toString().contains('Current playing Animation')) {
      List<String> parts = message.split('&');
      Map<String, String> result = {};
      for (var part in parts) {
        List<String> keyValue =
            part.split('=>').map((str) => str.trim()).toList();
        if (keyValue.length == 2) {
          result[keyValue[0]] = keyValue[1];
        }
      }
      // final data = splitWords(message.toString());
      // String combinedData = data.join('  ');
      if (result["screen"] == "VideoTranscriptScreen") {
        final currentWord = result['Current playing Animation']!
            .replaceAll("[", "")
            .replaceAll("]", "")
            .split(", ");
        if (mounted) {
          // Add mounted check before accessing context
          context.read<VideoTranscriptionBloc>().add(UpdateCurrentAnimationText(
              animationText: currentWord.toString()));
        }
      }
    } else if (message.toString().contains('switchtoIdle')) {
      Future.delayed(const Duration(seconds: 3), () {
        print("UpdateCurrentAnimationText => 3s");
        if (mounted) {
          // Add mounted check before accessing context
          context
              .read<VideoTranscriptionBloc>()
              .add(const UpdateCurrentAnimationText(animationText: ""));
        }
      });
    } else if (message.toString().contains("Current Animation")) {
      currentMessageList.add(message.toString().split("=>").last);
      if (mounted) {
        // Add mounted check before accessing context
        pushToSecond(message.toString().split("=>").last);
      }
      if (messagesFromUnity.length == currentMessageList.length && mounted) {
        if (mounted &&
            !context.read<VideoTranscriptionBloc>().isManuallyPaused) {
          _selectedTabIndex == 0
              ? _youtubePlayerController?.play()
              : _chewieController?.play();
          currentIndex.value = 0;
          messagesFromUnity.clear();
          currentMessageList.clear();
          context.read<VideoTranscriptionBloc>().rootWords.clear();
        }
      }
    }
  }

  List<String> splitWords(String message) {
    const prefix = "Current playing Animation =>";
    if (message.startsWith(prefix)) {
      String words = message.substring(prefix.length).trim();
      return words.split(' ');
    }
    return [];
  }

  void onUnityCreated(controller) {
    _unityWidgetController = controller;
    setCurrentUnityController(controller);
  }

  void _loadVideo([String? videoUrl]) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final url = videoUrl ?? _videoLinkController.text.trim();
      final videoId = YoutubePlayer.convertUrlToId(url);

      if (videoId != null) {
        // _youtubePlayerController?.dispose(); // dispose previous
        setState(() {
          _youtubePlayerController = YoutubePlayerController(
            initialVideoId: videoId,
            flags: const YoutubePlayerFlags(
              disableDragSeek: true,
              autoPlay: true,
              mute: false,
            ),
          );
        });
      } else {
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('invalid_youtube_url'.tr())),
        // );
      }
    });
  }

  Future<void> _loadLocalVideo() async {
    _videoController =
        VideoPlayerController.file(File(_videoFile?.path! ?? ""));

    // Wait for initialization before building Chewie
    await _videoController?.initialize();

    // Initially build Chewie with current bloc state
    final isVideoSelected =
        context.read<VideoTranscriptionBloc>().isVideoTranscriptionCompleted;

    _updateChewieControls(isVideoSelected);

    if (mounted) {
      setState(() {}); // UI rebuilds when ready
    }

    // Add listener to bloc so Chewie updates dynamically
    context
        .read<VideoTranscriptionBloc>()
        .add(AddChewieControllerListener(controller: _videoController!));
  }

  void _updateChewieControls(bool showControls) {
    if (_videoController == null) return;

    // Dispose old ChewieController if exists
    _chewieController?.dispose();

    _chewieController = ChewieController(
      videoPlayerController: _videoController!,
      autoPlay: true,
      looping: false,
      allowFullScreen: false,
      allowMuting: true,
      allowPlaybackSpeedChanging: false,
      showControls: showControls,
      materialProgressColors: ChewieProgressColors(
        playedColor: Colors.red,
        handleColor: Colors.redAccent,
        backgroundColor: Colors.grey,
        bufferedColor: Colors.lightGreen,
      ),
    );

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _videoTranscriptionBloc = context.read<VideoTranscriptionBloc>();
    _videoTranscriptionBloc
        .add(ResetVideoTranscription(controller: _videoLinkController));
    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if ((state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused)) {
      stopCurrentAnimation();
      _youtubePlayerController?.pause();
    }
  }

  @override
  void dispose() {
    // Dispose controllers
    _videoLinkController.dispose();
    _focusNode.dispose();

    // Stop any ongoing animations
    try {
      _unityWidgetController?.postMessage(
          "SaudiCharacter", 'StopAnimations', "");
      if (currentUnityController == _unityWidgetController) {
        currentUnityController = null;
      }
    } catch (e) {
      print("Error stopping animations during dispose: $e");
    }

    // Dispose YouTube player
    if (_youtubePlayerController != null) {
      _youtubePlayerController!.dispose();
      _youtubePlayerController = null;
    }

    // Clear lists and reset state
    messagesFromUnity.clear();
    currentMessageList.clear();
    currentProcessingTexts.clear();
    currentIndex.dispose();

    // Clear bloc data
    if (_videoTranscriptionBloc != null) {
      _videoTranscriptionBloc.captionList = [];
      _videoTranscriptionBloc.rootWords.clear();
    }
    _scrollController.dispose();

    super.dispose();
  }

  void _scrollToActiveSection() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _scrollController.animateTo(
        activeSectionIndex * 130.0, // height + spacing
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    // Get current locale to force rebuild when language changes
    final currentLocale = context.locale;

    return BlocConsumer<VideoTranscriptionBloc, VideoTranscriptionState>(
      listener: (context, state) {
        print("state from video transcription screen => $state");
        if (state is VideoTranscriptionScreenError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is SendMessagesToVideoScreen) {
          print("state is SendMessagesToVideoScreen");
          print("******${state.message}*****");
          messagesFromUnity.add(state.message['root']);
          sendMessageToUnity(state.message);
        } else if (state is VideoTranscriptionSuccess && state.isDisableInput) {
          // Load video when cached data is available and inputs are disabled
          _loadVideo(state.videoId);
        } else if (state is VideoTranscriptionInitial) {
          // Clear YouTube player when state is reset to initial
          setState(() {
            _youtubePlayerController = null;
          });
        } else if (state is AnimationSpeedUpdated) {
          // Update Unity animation speed when the animation speed changes
          adjustAnimationSpeed(state.animationSpeed.toString());
        } else if (state is SliderValueUpdated) {
          // Update local slider value and Unity animation speed
          print(
              "SliderValueUpdated received: sliderValue=${state.sliderValue}, animationSpeed=${state.animationSpeed}");
          sliderValue = state.sliderValue;
          adjustAnimationSpeed(state.animationSpeed.toString());
        } else if (state is VideoTranscriptionLoaded) {
          final isCompleted = context
              .read<VideoTranscriptionBloc>()
              .isVideoTranscriptionCompleted;
          isCompleted ? _updateChewieControls(isCompleted) : _loadLocalVideo();
        }
      },
      builder: (context, state) {
        return Stack(
          children: [
            Container(
              height: size.height,
              width: size.width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(APP_BG),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  _buildHeader(context),
                  _buildTabBar(),
                  const SizedBox(height: 5),
                  _contentWidget(state, context),
                  const SizedBox(
                    height: 10,
                  ),
                  if (_youtubePlayerController != null &&
                      _selectedTabIndex == 0) ...[
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          gradient: LinearGradient(
                            colors: [
                              const Color(0XFF9064FC).withOpacity(0.5),
                              const Color(0XFF1E113A).withOpacity(0.8),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          )),
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      // height: size.height * 0.3,
                      child: _youtubePlayerController != null
                          ? Container(
                              padding: const EdgeInsets.all(10),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(13),
                                child: YoutubePlayer(
                                  bufferIndicator: null,
                                  bottomActions: const [],
                                  controller: _youtubePlayerController!,
                                  onReady: () {
                                    context.read<VideoTranscriptionBloc>().add(
                                          AddVideoListener(
                                              controller:
                                                  _youtubePlayerController!),
                                        );
                                  },
                                  onEnded: (_) {
                                    _youtubePlayerController
                                        ?.seekTo(Duration.zero);
                                    _youtubePlayerController?.pause();
                                  },
                                ),
                              ),
                            )
                          : const SizedBox(),
                    ),
                    const SizedBox(height: 5),
                    // Toggle button to show/hide input fields
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              stopCurrentAnimation();
                              _youtubePlayerController?.pause();
                              currentIndex.value = 0;
                              messagesFromUnity.clear();
                              currentMessageList.clear();
                              allWords = [];
                              lastWords = [];
                              activeSectionIndex = 0;
                              context
                                  .read<VideoTranscriptionBloc>()
                                  .rootWords
                                  .clear();
                              // Toggle input visibility
                              context.read<VideoTranscriptionBloc>().add(
                                    ToggleInputVisibility(
                                        showInput: state.isDisableInput),
                                  );
                              // Also hide YouTube widget when showing inputs
                              if (state.isDisableInput) {
                                setState(() {
                                  _youtubePlayerController = null;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF2A2157),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    state.isDisableInput
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    state.isDisableInput
                                        ? 'translate_a_new_video'.tr()
                                        : 'hide_video'.tr(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  if (_chewieController != null &&
                      _videoController!.value.isInitialized &&
                      _selectedTabIndex == 1) ...[
                    Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            gradient: LinearGradient(
                              colors: [
                                const Color(0XFF9064FC).withOpacity(0.5),
                                const Color(0XFF1E113A).withOpacity(0.8),
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            )),
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        // height: size.height * 0.3,
                        child: Container(
                          height: 200,
                          padding: const EdgeInsets.all(10),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(13),
                            child: Chewie(controller: _chewieController!),
                          ),
                        )),
                    const SizedBox(height: 5),
                    // Toggle button to show/hide input fields
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              stopCurrentAnimation();
                              _chewieController?.pause();
                              currentIndex.value = 0;
                              messagesFromUnity.clear();
                              currentMessageList.clear();
                              allWords = [];
                              lastWords = [];
                              activeSectionIndex = 0;
                              context
                                  .read<VideoTranscriptionBloc>()
                                  .rootWords
                                  .clear();
                              stopCurrentAnimation();
                              // Toggle input visibility
                              context.read<VideoTranscriptionBloc>().add(
                                    const ToggleInputVisibility(
                                        showInput: false),
                                  );
                              // Also hide YouTube widget when showing inputs
                              if (state.isDisableInput) {
                                setState(() {
                                  _chewieController = null;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF2A2157),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    state.isDisableInput
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    state.isDisableInput
                                        ? 'translate_a_new_video'.tr()
                                        : 'hide_video'.tr(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Container _contentWidget(
      VideoTranscriptionState state, BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      // padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: LinearGradient(
            colors: [
              const Color(0XFF9064FC).withOpacity(0.5),
              const Color(0XFF1E113A).withOpacity(0.8),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 5),

          // Video Link Input - Hide when isDisableInput is true

          if (_selectedTabIndex == 0 && !state.isDisableInput) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                'video_link'.tr(),
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                    fontFamily: FONT_FAMILY_INTER),
              ),
            ),
            const SizedBox(height: 10),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: const Color(0xFF2A2157),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.white.withOpacity(0.1)),
              ),
              child: TextField(
                ignorePointers: state.isDataLoading,
                controller: _videoLinkController,
                focusNode: _focusNode,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'youtube_link_hint'.tr(),
                  hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: CustomGradientButton(
                onPressed: () {
                  if (_videoLinkController.text.isNotEmpty) {
                    _focusNode.unfocus();
                    currentIndex.value = 0;
                    messagesFromUnity.clear();
                    currentMessageList.clear();
                    context.read<VideoTranscriptionBloc>().rootWords.clear();
                    context.read<VideoTranscriptionBloc>().add(
                        StartVideoTranscription(
                            videoId: _videoLinkController.text.trim(),
                            controller: _videoLinkController));
                  }
                },
                label:
                    state.isDataLoading ? "translating".tr() : "translate".tr(),
                gradientColors: !state.isDataLoading
                    ? [purpleMimosa, lightRoyalBlue]
                    : [Colors.grey[900]!, Colors.grey],
              ),
            ),
            const SizedBox(height: 20),
          ],

          if (_selectedTabIndex == 1 && _chewieController == null) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                'select_video_file'.tr(),
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                    fontFamily: FONT_FAMILY_INTER),
              ),
            ),
            const SizedBox(height: 10),
            GestureDetector(
              onTap: () async {
                FilePickerResult? result = await FilePicker.platform.pickFiles(
                  type: FileType.video,
                  allowMultiple: false,
                );
                if (result != null && result.files.isNotEmpty) {
                  _videoFile = result.files.first;
                  context
                      .read<VideoTranscriptionBloc>()
                      .add(const UpdateVideoSelection(isVideoSelected: true));
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: const Color(0XFF221B67),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: ValueListenableBuilder(
                          valueListenable: progressNotifier,
                          builder: (ctx, value, _) {
                            return state.isVideoUploading
                                ? Container(
                                    height: 30,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 10),
                                    child: LinearProgressIndicator(
                                      minHeight: 5,
                                      backgroundColor:
                                          Colors.white.withOpacity(0.2),
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white.withOpacity(0.8),
                                      ),
                                      value: value,
                                    ),
                                  )
                                : Row(
                                    // mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          state.isVideoSelected
                                              ? _videoFile!.name
                                              : "upload_video_file".tr(),
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.7),
                                            fontSize: 14,
                                            fontFamily: FONT_FAMILY,
                                          ),
                                          maxLines: 2,
                                          // overflow: TextOverflow.clip,
                                        ),
                                      ),
                                      Image.asset(UPLOAD,
                                          height: 25, width: 25),
                                    ],
                                  );
                          })),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: CustomGradientButton(
                onPressed: () {
                  if (_videoFile != null) {
                    context.read<VideoTranscriptionBloc>().add(UploadChunkData(
                        file: _videoFile!,
                        progressNotifier: progressNotifier,
                        sourceType: "uploaded_video"));
                  }
                },
                label: state.isVideoUploading
                    ? "translating".tr()
                    : "translate".tr(),
                gradientColors: !state.isVideoUploading
                    ? [purpleMimosa, lightRoyalBlue]
                    : [Colors.grey[900]!, Colors.grey],
              ),
            ),
            const SizedBox(height: 20),
          ],
          // Character avatar
          _buildCharacterAvatar(context),

          const SizedBox(height: 10),
          // Status text
          _buildStatusText(state, MediaQuery.sizeOf(context)),
        ],
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    VoidCallback onTap,
    Color backgroundColor,
  ) {
    return Material(
      // Circle with your background color.
      color: backgroundColor,
      shape: const CircleBorder(),
      clipBehavior: Clip.antiAlias, // keeps splash inside the circle
      child: InkWell(
        onTap: onTap,
        customBorder: const CircleBorder(), // matches the shape
        splashColor: Colors.white24, // optional – tweak to taste
        highlightColor: Colors.white10, // optional – tweak to taste
        child: SizedBox(
          width: 40,
          height: 40,
          child: Icon(
            icon,
            color: Colors.white,
            size: 18,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    // Get current locale to force rebuild when language changes
    final currentLocale = context.locale;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Back button
          GestureDetector(
              onTap: () {
                bottomBarIndex.value = 0;
                _youtubePlayerController?.dispose();
                // Reset VideoTranscription state to default
                context.read<VideoTranscriptionBloc>().add(
                      ResetVideoTranscription(controller: _videoLinkController),
                    );
              },
              child: Image.asset(BACK_BUTTON, height: 25, width: 25)),

          const SizedBox(width: 15),

          // Title
          Text(
            'video_translation'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: FONT_FAMILY,
            ),
          ),

          const Spacer(),

          // Info button
          GestureDetector(
              onTap: () {
                // Show info dialog or tooltip
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('video_info'.tr()),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              child: Image.asset(INFO, height: 25, width: 25)),

          const SizedBox(width: 10),

          // Language toggle button
          // BlocBuilder<LanguageBloc, LanguageState>(
          //   builder: (context, state) {
          //     return GestureDetector(
          //       onTap: () {
          //         // Toggle language
          //         context.read<LanguageBloc>().add(ToggleLanguage());

          //         // Set locale based on current language
          //         final newLocale = state.locale.languageCode == 'en'
          //             ? const Locale('ar')
          //             : const Locale('en');

          //         context.setLocale(newLocale);

          //         // Force rebuild by triggering a state change
          //         if (mounted) setState(() {});

          //         // Force rebuild of the entire app
          //         WidgetsBinding.instance.addPostFrameCallback((_) {
          //           // Force rebuild of all ValueListenableBuilder widgets
          //           bottomBarIndex.notifyListeners();
          //           currentNavItems.notifyListeners();
          //           if (showMorePopup != null) showMorePopup.notifyListeners();
          //           if (isCustomizationMode != null)
          //             isCustomizationMode.notifyListeners();
          //           currentIndex.notifyListeners();
          //         });

          //         // Debug print to verify locale change
          //         print("Language changed to: ${newLocale.languageCode}");
          //       },
          //       child: Container(
          //         padding: const EdgeInsets.all(8),
          //         decoration: BoxDecoration(
          //           color: Colors.white.withOpacity(0.1),
          //           borderRadius: BorderRadius.circular(20),
          //         ),
          //         child: Text(
          //           state.locale.languageCode == 'en' ? 'AR' : 'EN',
          //           style: const TextStyle(
          //             color: Colors.white,
          //             fontSize: 14,
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //     );
          //   },
          // ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(30),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          _tabTitles.length,
          (index) => _buildTabItem(index),
        ),
      ),
    );
  }

  Widget _buildTabItem(int index) {
    final isSelected = _selectedTabIndex == index;

    return Flexible(
      child: GestureDetector(
        onTap: () async {
          if (!mounted) return;
          context.read<VideoTranscriptionBloc>().add(
                const UpdateCurrentAnimationText(animationText: ""),
              );

          setState(() {
            _selectedTabIndex = index;
            _videoController = null;
            _chewieController = null;
            _videoFile = null;
          });
          _chewieController?.pause();
          _videoController?.pause();
          context
              .read<VideoTranscriptionBloc>()
              .add(const UpdateVideoSelection(isVideoSelected: false));
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color:
                    isSelected ? const Color(0xFF7B6FFF) : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                IC_RECORD,
                height: 15,
                width: 15,
              ),
              const SizedBox(width: 5),
              Flexible(
                child: Text(
                  _tabTitles[index].tr(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withOpacity(0.6),
                    fontWeight: isSelected ? FontWeight.w400 : FontWeight.w300,
                    fontSize: 12,
                    fontFamily: FONT_FAMILY_INTER,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCharacterAvatar(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    return Container(
      height: screenHeight * 0.22, // Increased height for better proportions
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.white.withOpacity(0.05),
          ],
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Character image in ClipRRect for rounded corners
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: UnityWidget(
              onUnityCreated: onUnityCreated,
              onUnityMessage: onUnityMessage,
            ),
          ),

          // Control buttons positioned to the right
          Positioned(
            right: -25, // Position outside the container
            top: screenHeight * 0.025, // Center vertically
            child: BlocBuilder<VideoTranscriptionBloc, VideoTranscriptionState>(
              builder: (context, state) {
                final animationSpeed = state.animationSpeed;
                // Determine if buttons should be disabled based on animation speed
                final isIncreaseDisabled = animationSpeed >= 2.0;
                final isDecreaseDisabled = animationSpeed <= 0.5;
                return Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(23),
                    color: const Color(0XFF49446C).withOpacity(0.7),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // _buildControlButton(
                      //     Icons.refresh, () {}, const Color(0XFF7B6FFF)),
                      // const SizedBox(height: 10),
                      _verticalSlider(),
                    ],
                  ),
                );
              },
            ),
          ),
          // Add vertical slider for animation speed control
        ],
      ),
    );
  }

  Widget _buildStatusText(VideoTranscriptionState state, Size size) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      height: 120,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: BlocBuilder<VideoTranscriptionBloc, VideoTranscriptionState>(
        builder: (context, state) {
          List<String> words = state.currentAnimation
              .replaceAll('[', '')
              .replaceAll(']', '')
              .split(', ')
              .where((e) => e.trim().isNotEmpty)
              .toList();

          if (words.isNotEmpty &&
              (allWords.isEmpty || !listEquals(words, lastWords))) {
            lastWords = words;
            allWords.add(words);
            activeSectionIndex = allWords.length - 1;
            _scrollToActiveSection();
          }

          return SizedBox(
            height: 120,
            width: size.width,
            child: ListView.builder(
              controller: _scrollController,
              itemCount: allWords.length,
              itemBuilder: (context, sectionIndex) {
                final segment = allWords[sectionIndex];
                return Container(
                  width: size.width * 0.7,
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ValueListenableBuilder(
                    valueListenable: currentIndex,
                    builder: (context, currentActiveIndex, _) {
                      return Text.rich(
                        TextSpan(
                          children:
                              List<TextSpan>.generate(segment.length, (index) {
                            return TextSpan(
                              text:
                                  '${segment[index]}${index < segment.length - 1 ? ' ' : ''}',
                              style: TextStyle(
                                backgroundColor:
                                    (sectionIndex == activeSectionIndex &&
                                            index == currentActiveIndex)
                                        ? const Color(0XFF755BFF)
                                        : null,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            );
                          }),
                        ),
                        textDirection: ui.TextDirection.rtl,
                        maxLines: 4,
                      );
                    },
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _verticalSlider() {
    return Container(
      width: 50, // Made wider for better visibility
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        // Enhanced shadow for half-in, half-out design visibility
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 6,
            offset: const Offset(-2, 2), // Slight left offset for depth
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 12,
            offset: const Offset(-4, 4), // Larger shadow for more depth
          ),
        ],
      ),
      height: MediaQuery.of(context).size.height * 0.12,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFE8E8E8),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SliderTheme(
                  data: SliderThemeData(
                    overlayShape: SliderComponentShape.noOverlay,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 15,
                      disabledThumbRadius: 20,
                    ),
                  ),
                  child: BlocBuilder<VideoTranscriptionBloc,
                      VideoTranscriptionState>(
                    builder: (context, state) {
                      if (state is SliderValueUpdated) {
                        sliderValue = state.sliderValue;
                      }
                      return Slider(
                        activeColor: const Color(0xFFE8E8E8),
                        inactiveColor: const Color(0xFFE8E8E8),
                        thumbColor: const Color(0xFF054DA4),
                        value: sliderValue.clamp(0.0, 1.5),
                        min: 0,
                        max: 1.5,
                        divisions: 3,
                        onChanged: (val) {
                          // Update local state immediately for smooth UI
                          setState(() {
                            sliderValue = val;
                          });
                          // Then emit BLoC event for Unity integration
                          context
                              .read<VideoTranscriptionBloc>()
                              .add(UpdateSliderValue(value: val));
                        },
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
